#!/usr/bin/env python3
"""
Test each website individually.
"""

from webscraptest import tech_news
import json

def test_site(url, site_name):
    """Test a single website."""
    print(f"\n=== Testing {site_name} ===")
    print(f"URL: {url}")
    
    try:
        articles = tech_news([url])
        print(f"Found {len(articles)} relevant articles")
        
        for i, article in enumerate(articles, 1):
            print(f"{i}. {article['title']}")
            print(f"   URL: {article['url']}")
        
        return articles
    except Exception as e:
        print(f"Error: {e}")
        return []

def main():
    """Test each website individually."""
    
    sites = [
        ("https://news.ycombinator.com/", "Hacker News"),
        ("https://techcrunch.com/category/artificial-intelligence/", "TechCrunch AI"),
        ("https://www.theverge.com/", "The Verge")
    ]
    
    all_articles = []
    
    for url, name in sites:
        articles = test_site(url, name)
        all_articles.extend(articles)
    
    print(f"\n=== TOTAL RESULTS ===")
    print(f"Found {len(all_articles)} total articles across all sites")
    
    if all_articles:
        with open('individual_site_results.json', 'w', encoding='utf-8') as f:
            json.dump(all_articles, f, indent=2, ensure_ascii=False)
        print("Results saved to individual_site_results.json")

if __name__ == "__main__":
    main()
