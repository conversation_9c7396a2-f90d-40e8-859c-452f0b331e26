#!/usr/bin/env python3
"""
Debug TechCrunch specifically.
"""

import requests
from bs4 import BeautifulSoup

def debug_techcrunch():
    """Debug TechCrunch article extraction."""
    url = "https://techcrunch.com/category/artificial-intelligence/"
    
    try:
        response = requests.get(url, timeout=30, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, "html.parser")
        
        print("=== TechCrunch Debug ===")
        
        # Try different selectors
        selectors = [
            '.post',
            '.wp-block-post',
            'article',
            '.loop-card',
            '.wp-block-techcrunch-card'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            print(f"\n{selector}: {len(elements)} elements")
            
            if elements and len(elements) > 0:
                first = elements[0]
                print(f"  Classes: {first.get('class', [])}")
                
                # Try to find title
                title_selectors = ['h3 a', 'h2 a', 'h1 a', 'a']
                for ts in title_selectors:
                    title_elem = first.select_one(ts)
                    if title_elem:
                        title = title_elem.get_text().strip()
                        href = title_elem.get('href', 'No href')
                        print(f"  Title ({ts}): {title}")
                        print(f"  URL: {href}")
                        break
                else:
                    print("  No title found")
        
        # Test the actual function logic
        print("\n=== Testing actual function logic ===")
        article_elements = soup.select('.post')
        print(f"Found {len(article_elements)} .post elements")
        
        for i, article in enumerate(article_elements[:3]):
            print(f"\nArticle {i+1}:")
            title_element = article.select_one('h3 a, h2 a')
            if title_element:
                title = title_element.get_text().strip()
                print(f"  Title: {title}")
                print(f"  URL: {title_element.get('href', 'No href')}")
            else:
                print("  No title element found")
                # Show what's in the article
                all_links = article.find_all('a', href=True)
                print(f"  Found {len(all_links)} links:")
                for j, link in enumerate(all_links[:3]):
                    print(f"    {j+1}. {link.get_text().strip()[:50]}... -> {link['href'][:80]}...")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_techcrunch()
