#!/usr/bin/env python3
"""
Inspect HTML structure of websites to find correct selectors.
"""

import requests
from bs4 import BeautifulSoup

def inspect_website(url, site_name):
    """Inspect the HTML structure of a website."""
    print(f"\n=== Inspecting {site_name} ({url}) ===")
    
    try:
        response = requests.get(url, timeout=30, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, "html.parser")
        
        # Look for common article containers
        print("\nLooking for article containers:")
        
        # Try different selectors
        selectors_to_try = [
            'article',
            '.post',
            '.entry',
            '.story',
            '.item',
            '[class*="post"]',
            '[class*="article"]',
            '[class*="story"]',
            '[class*="card"]'
        ]
        
        for selector in selectors_to_try:
            elements = soup.select(selector)
            if elements:
                print(f"  {selector}: {len(elements)} elements")
                
                # Show first element's structure
                if len(elements) > 0:
                    first_elem = elements[0]
                    print(f"    First element classes: {first_elem.get('class', [])}")
                    
                    # Look for title elements within
                    title_candidates = first_elem.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    if title_candidates:
                        print(f"    Found {len(title_candidates)} heading elements")
                        for i, title in enumerate(title_candidates[:2]):
                            print(f"      {i+1}. {title.name}: {title.get_text().strip()[:100]}...")
                            if title.find('a'):
                                print(f"         Contains link: {title.find('a').get('href', 'No href')}")
                    
                    # Look for links
                    links = first_elem.find_all('a', href=True)
                    if links:
                        print(f"    Found {len(links)} links")
                        for i, link in enumerate(links[:3]):
                            print(f"      {i+1}. {link.get_text().strip()[:50]}... -> {link['href'][:100]}...")
                    
                    print()
        
        # Look specifically for title patterns
        print("\nLooking for title patterns:")
        title_selectors = [
            'h1 a', 'h2 a', 'h3 a',
            '.title a', '.headline a',
            '[class*="title"] a',
            '[class*="headline"] a'
        ]
        
        for selector in title_selectors:
            elements = soup.select(selector)
            if elements:
                print(f"  {selector}: {len(elements)} elements")
                if len(elements) > 0:
                    print(f"    Example: {elements[0].get_text().strip()[:100]}...")
        
    except Exception as e:
        print(f"Error inspecting {site_name}: {e}")

def main():
    """Inspect multiple websites."""
    
    websites = [
        ("https://techcrunch.com/category/artificial-intelligence/", "TechCrunch AI"),
        ("https://www.theverge.com/", "The Verge"),
        ("https://news.ycombinator.com/", "Hacker News")
    ]
    
    for url, name in websites:
        inspect_website(url, name)

if __name__ == "__main__":
    main()
