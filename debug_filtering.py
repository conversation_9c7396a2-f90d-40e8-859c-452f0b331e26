#!/usr/bin/env python3
"""
Debug the filtering process to see why articles are being excluded.
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
from webscraptest import _extract_date, _is_today, is_relevant_article
import datetime

def debug_filtering(url, site_name, max_articles=5):
    """Debug the filtering process for a website."""
    print(f"\n=== Debugging {site_name} ({url}) ===")
    
    try:
        response = requests.get(url, timeout=30, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, "html.parser")
        
        # Website-specific article detection
        if 'news.ycombinator.com' in url:
            article_elements = soup.select('.athing')
        elif 'techcrunch.com' in url:
            article_elements = soup.select('.post')
        elif 'theverge.com' in url:
            article_elements = soup.select('.duet--content-cards--content-card')
        else:
            article_elements = soup.select('article')
        
        print(f"Found {len(article_elements)} article elements")
        
        base_url = urlparse(url)
        base_domain = f"{base_url.scheme}://{base_url.netloc}"
        
        passed_filters = 0
        
        for i, article in enumerate(article_elements[:max_articles]):
            print(f"\n--- Article {i+1} ---")
            
            # Extract title
            title_element = None
            if 'news.ycombinator.com' in url:
                title_element = article.select_one('.titleline > a, .title a')
            elif 'techcrunch.com' in url:
                title_element = article.select_one('h3 a, h2 a')
            elif 'theverge.com' in url:
                title_element = article.select_one('a[href*="/"]')
                if title_element and ('#comments' in title_element.get('href', '') or 
                                    title_element.get_text().strip() in ['', 'Comments', 'Comment Icon Bubble']):
                    all_links = article.select('a[href*="/"]')
                    for link in all_links:
                        if '#comments' not in link.get('href', '') and link.get_text().strip():
                            title_element = link
                            break
            
            if not title_element:
                title_element = article.select_one('h2 a, h3 a, h1 a, .title a, .headline a')
            if not title_element:
                title_element = article.find(['h1', 'h2', 'h3', 'h4'])
            
            if not title_element:
                print("❌ No title element found")
                continue
            
            title = title_element.get_text().strip()
            if not title or title == "No title found":
                print("❌ Empty or invalid title")
                continue
            
            print(f"✓ Title: {title}")
            
            # Extract URL
            link = None
            if title_element.name == 'a' and title_element.has_attr('href'):
                link = title_element['href']
            
            if not link:
                parent_link = title_element.find_parent('a')
                if parent_link and parent_link.has_attr('href'):
                    link = parent_link['href']
            
            if link and not link.startswith(('http://', 'https://')):
                link = urljoin(base_domain, link)
            
            if not link:
                print("❌ No URL found")
                continue
            
            print(f"✓ URL: {link}")
            
            # Extract and check date
            pub_date = _extract_date(article)
            print(f"📅 Date extracted: {pub_date}")
            
            if pub_date:
                is_today = _is_today(pub_date)
                print(f"📅 Is today: {is_today}")
                if not is_today:
                    print("❌ Not from today - FILTERED OUT")
                    continue
            else:
                print("❌ No date found - FILTERED OUT")
                continue
            
            # Check keyword relevance
            article_dict = {'title': title}
            is_relevant = is_relevant_article(article_dict)
            print(f"🔍 Keyword relevant: {is_relevant}")
            
            if not is_relevant:
                print("❌ Not keyword relevant - FILTERED OUT")
                continue
            
            print("✅ PASSED ALL FILTERS!")
            passed_filters += 1
        
        print(f"\nSummary: {passed_filters}/{min(max_articles, len(article_elements))} articles passed all filters")
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Debug filtering for multiple sites."""
    
    print("=== DEBUGGING ARTICLE FILTERING ===")
    print(f"Today's date: {datetime.datetime.now().strftime('%Y-%m-%d')}")
    
    sites = [
        ("https://news.ycombinator.com/", "Hacker News"),
        ("https://techcrunch.com/category/artificial-intelligence/", "TechCrunch AI"),
        ("https://www.theverge.com/", "The Verge")
    ]
    
    for url, name in sites:
        debug_filtering(url, name, max_articles=3)

if __name__ == "__main__":
    main()
