#!/usr/bin/env python3
"""
Debug script to test URL extraction improvements.
"""

from webscraptest import tech_news
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
import json

def debug_url_extraction(url, max_articles=5):
    """Debug URL extraction for a specific website."""
    print(f"\n=== Debugging URL extraction for {url} ===")
    
    try:
        response = requests.get(url, timeout=30, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, "html.parser")
        
        # Website-specific article detection
        article_elements = []
        
        if 'news.ycombinator.com' in url:
            article_elements = soup.select('.athing')
            print(f"Found {len(article_elements)} HN articles using .athing selector")
        elif 'techcrunch.com' in url:
            article_elements = soup.select('article, .post-block, .wp-block-tc23-post-picker')
            print(f"Found {len(article_elements)} TechCrunch articles")
        elif 'theverge.com' in url:
            article_elements = soup.select('article, .duet--content-cards--content-card, .c-entry-box--compact')
            print(f"Found {len(article_elements)} Verge articles")
        
        base_url = urlparse(url)
        base_domain = f"{base_url.scheme}://{base_url.netloc}"
        
        successful_extractions = 0
        
        for i, article in enumerate(article_elements[:max_articles]):
            print(f"\n--- Article {i+1} ---")
            
            # Extract title
            title_element = None
            if 'news.ycombinator.com' in url:
                title_element = article.select_one('.titleline > a, .title a')
            elif 'techcrunch.com' in url:
                title_element = article.select_one('h2 a, h3 a, .post-block__title a, .post-block__title__link')
            elif 'theverge.com' in url:
                title_element = article.select_one('h2 a, h3 a, .c-entry-box--compact__title a')
            
            if not title_element:
                title_element = article.select_one('h2 a, h3 a, h1 a, .title a, .headline a')
            if not title_element:
                title_element = article.find(['h1', 'h2', 'h3', 'h4'])
            
            if title_element:
                title = title_element.get_text().strip()
                print(f"Title: {title}")
                
                # Extract URL
                link = None
                
                # Method 1: Title element is a link
                if title_element.name == 'a' and title_element.has_attr('href'):
                    link = title_element['href']
                    print(f"✓ Method 1 - URL from title element: {link}")
                
                # Method 2: Parent link
                if not link:
                    parent_link = title_element.find_parent('a')
                    if parent_link and parent_link.has_attr('href'):
                        link = parent_link['href']
                        print(f"✓ Method 2 - URL from parent link: {link}")
                
                # Method 3: Website-specific
                if not link:
                    if 'news.ycombinator.com' in url:
                        title_link = article.select_one('.titleline > a')
                        if title_link and title_link.has_attr('href'):
                            link = title_link['href']
                            print(f"✓ Method 3 - HN specific: {link}")
                    elif 'techcrunch.com' in url:
                        tc_link = article.select_one('h2 a, h3 a, .post-block__title__link')
                        if tc_link and tc_link.has_attr('href'):
                            link = tc_link['href']
                            print(f"✓ Method 3 - TC specific: {link}")
                
                # Method 4: Text matching
                if not link:
                    all_links = article.find_all('a', href=True)
                    for a_tag in all_links:
                        link_text = a_tag.get_text().strip().lower()
                        if title.lower() in link_text or link_text in title.lower():
                            link = a_tag['href']
                            print(f"✓ Method 4 - Text matching: {link}")
                            break
                
                # Method 5: First meaningful link
                if not link:
                    all_links = article.find_all('a', href=True)
                    for a_tag in all_links:
                        href = a_tag['href']
                        if href and not href.startswith('#') and not href.startswith('javascript:'):
                            link = href
                            print(f"✓ Method 5 - First meaningful: {link}")
                            break
                
                if link:
                    # Make absolute
                    if not link.startswith(('http://', 'https://')):
                        link = urljoin(base_domain, link)
                        print(f"Made absolute: {link}")
                    
                    successful_extractions += 1
                else:
                    print("✗ No URL found")
            else:
                print("✗ No title found")
        
        print(f"\nSummary: {successful_extractions}/{min(max_articles, len(article_elements))} successful URL extractions")
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Test URL extraction on different websites."""
    
    test_urls = [
        "https://news.ycombinator.com/",
        "https://techcrunch.com/category/artificial-intelligence/",
        "https://www.theverge.com/"
    ]
    
    for url in test_urls:
        debug_url_extraction(url, max_articles=3)
    
    print("\n" + "="*50)
    print("Testing the improved tech_news function:")
    
    articles = tech_news(test_urls)
    print(f"\nFound {len(articles)} articles with valid URLs:")
    
    for i, article in enumerate(articles, 1):
        print(f"{i}. {article['title']}")
        print(f"   URL: {article['url']}")
        print()
    
    if articles:
        with open('improved_results.json', 'w', encoding='utf-8') as f:
            json.dump(articles, f, indent=2, ensure_ascii=False)
        print("Results saved to improved_results.json")

if __name__ == "__main__":
    main()
