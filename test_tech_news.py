#!/usr/bin/env python3
"""
Test script for the simplified tech_news function.
"""

from webscraptest import tech_news, _extract_date, _is_today, is_relevant_article
import json
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse

def test_basic_scraping():
    """Test basic scraping without date filtering."""
    print("Testing basic scraping functionality...")

    # Test with just one URL
    test_url = "https://news.ycombinator.com/"

    try:
        response = requests.get(test_url, timeout=30, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        response.raise_for_status()

        soup = BeautifulSoup(response.content, "html.parser")
        article_elements = soup.select('article')

        if not article_elements:
            # Try HN specific selector
            article_elements = soup.select('.athing')

        print(f"Found {len(article_elements)} article elements")

        # Test a few articles
        for i, article in enumerate(article_elements[:10]):
            title_element = article.select_one('.title a, h1, h2, h3')
            if title_element:
                title = title_element.get_text().strip()
                print(f"Article {i+1}: {title}")

                # Test keyword filtering
                article_dict = {'title': title}
                is_relevant = is_relevant_article(article_dict)
                print(f"  Relevant: {is_relevant}")

                # Check for specific keywords
                title_lower = title.lower()
                found_keywords = []
                from webscraptest import INCLUDE_KEYWORDS
                for keyword in INCLUDE_KEYWORDS:
                    if keyword.lower() in title_lower:
                        found_keywords.append(keyword)
                if found_keywords:
                    print(f"  Found keywords: {found_keywords}")

                # Test date extraction
                pub_date = _extract_date(article)
                print(f"  Date: {pub_date}")
                if pub_date:
                    is_today = _is_today(pub_date)
                    print(f"  Is today: {is_today}")
                print()

    except Exception as e:
        print(f"Error in basic scraping test: {e}")

def main():
    """Test the tech_news function with a few URLs."""

    # First test basic functionality
    test_basic_scraping()

    # Test URLs
    test_urls = [
        "https://news.ycombinator.com/",
        "https://techcrunch.com/category/artificial-intelligence/"
    ]

    print("\nTesting tech_news function...")
    print(f"Scraping {len(test_urls)} URLs for today's tech articles...")

    try:
        # Call the tech_news function
        articles = tech_news(test_urls)

        print(f"\nFound {len(articles)} relevant tech articles from today:")

        # Display results
        for i, article in enumerate(articles, 1):
            print(f"\n{i}. {article['title']}")
            print(f"   URL: {article['url']}")

        # Save results to JSON file
        if articles:
            with open('test_results.json', 'w', encoding='utf-8') as f:
                json.dump(articles, f, indent=2, ensure_ascii=False)
            print(f"\nResults saved to test_results.json")
        else:
            print("\nNo articles found matching the criteria.")

    except Exception as e:
        print(f"Error occurred: {e}")

if __name__ == "__main__":
    main()
